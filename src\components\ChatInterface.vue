<template>
  <div class="chat-container">
    <div class="lingnan-card corner-ornaments ruyi-cloud guangfu-window">
      <div class="chat-header">
        <div class="header-seal">
          <h2 class="lingnan-title seal-style">瑶绣智能问答助手</h2>
          <div class="assistant-badge">智能助手</div>
        </div>
        <div class="title-decoration">
          <span class="decoration-symbol">❋</span>
          <div class="ruyi-divider"></div>
          <span class="decoration-symbol">❋</span>
        </div>
      </div>
      <p class="chat-description">
        我是一位专业的瑶绣文化智能助手，可以为您解答关于岭南瑶绣的历史、工艺、纹样、技法等各种问题。
      </p>
      
      <div class="quick-questions">
        <h3>快速提问</h3>
        <div class="quick-question-buttons">
          <button 
            v-for="question in quickQuestions" 
            :key="question.id"
            @click="askQuickQuestion(question.text)"
            class="quick-question-btn"
          >
            {{ question.text }}
          </button>
        </div>
      </div>
      
      <div class="chat-messages guangfu-lattice wan-pattern" ref="messagesContainer">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="['message', message.role]"
        >
          <div class="message-content">
            <div class="message-avatar">
              {{ message.role === 'user' ? '👤' : '🤖' }}
            </div>
            <div class="message-text">
              {{ message.content }}
            </div>
          </div>
        </div>
        <div v-if="isLoading" class="message assistant">
          <div class="message-content">
            <div class="message-avatar">🤖</div>
            <div class="message-text typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="chat-input">
        <input 
          v-model="userInput"
          @keyup.enter="sendMessage"
          placeholder="请输入您的问题..."
          :disabled="isLoading"
        />
        <button 
          @click="sendMessage"
          :disabled="isLoading || !userInput.trim()"
          class="seal-button"
        >
          发送
        </button>
      </div>
      
      <div class="chat-features">
        <div class="feature-item">
          <span class="feature-icon">🎨</span>
          <span class="feature-text">纹样识别</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🧵</span>
          <span class="feature-text">工艺讲解</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">📚</span>
          <span class="feature-text">历史渊源</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🔍</span>
          <span class="feature-text">文化解析</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { chatApi } from '@/api'

const userInput = ref('')
const messages = ref([])
const isLoading = ref(false)
const messagesContainer = ref(null)

const quickQuestions = [
  { id: 1, text: '瑶绣的历史起源是什么？' },
  { id: 2, text: '瑶绣有哪些传统纹样？' },
  { id: 3, text: '瑶绣的工艺特点是什么？' },
  { id: 4, text: '如何辨别瑶绣的真伪？' },
  { id: 5, text: '瑶绣的文化意义是什么？' },
  { id: 6, text: '现代瑶绣的发展状况如何？' }
]

const askQuickQuestion = (question) => {
  userInput.value = question
  sendMessage()
}

const sendMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return
  
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: userInput.value
  }
  
  messages.value.push(userMessage)
  const currentInput = userInput.value
  userInput.value = ''
  
  isLoading.value = true
  scrollToBottom()
  
  try {
    const response = await chatApi.sendMessage(currentInput)
    
    const assistantMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: response.data.reply
    }
    
    messages.value.push(assistantMessage)
  } catch (error) {
    console.error('发送消息失败:', error)
    
    const errorMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: '抱歉，我遇到了一些问题。请稍后再试，或者您可以尝试重新提问。'
    }
    
    messages.value.push(errorMessage)
  } finally {
    isLoading.value = false
    scrollToBottom()
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

onMounted(() => {
  const welcomeMessage = {
    id: Date.now(),
    role: 'assistant',
    content: '您好！我是瑶绣智能问答助手，很高兴为您服务。我可以为您解答关于岭南瑶绣的各种问题，包括历史渊源、工艺技法、纹样寓意、文化价值等。请问您想了解什么呢？'
  }
  
  messages.value.push(welcomeMessage)
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
.chat-header {
  text-align: center;
  margin-bottom: $spacing-lg;
}

.header-seal {
  position: relative;
  display: inline-block;
  padding: 20px 30px;
  background:
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($pearl-white, 0.9)),
    radial-gradient(circle at center, rgba($golden-yellow, 0.12) 0%, transparent 70%);
  border: 3px solid $bronze-gold;
  border-radius: 15px;
  box-shadow:
    inset 0 2px 4px rgba($golden-yellow, 0.3),
    0 4px 8px rgba(0,0,0,0.15);
  margin-bottom: 15px;
}

.assistant-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, $deep-jade, $jade-green);
  color: $ivory;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  font-family: $font-family-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  border: 2px solid $golden-yellow;
}

.seal-style {
  position: relative;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  color: $vermillion;
  margin: 0;

  &::after {
    background: linear-gradient(90deg, $vermillion, $golden-yellow, $deep-jade);
  }
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.decoration-symbol {
  color: $cinnabar-red;
  font-size: 1.5rem;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba($cinnabar-red, 0.5);
  }
  to {
    text-shadow: 0 0 10px rgba($cinnabar-red, 0.8), 0 0 15px rgba($imperial-yellow, 0.3);
  }
}

.chat-description {
  text-align: center;
  color: $text-secondary;
  margin-bottom: $spacing-lg;
  font-size: 1.1rem;
  line-height: 1.6;
  background: linear-gradient(135deg, rgba($pearl-white, 0.8), rgba($rice-paper, 0.9));
  padding: $spacing-md;
  border-radius: $border-radius-md;
  border: 1px solid rgba($guangfu-gold, 0.3);
}

.quick-questions {
  margin-bottom: $spacing-lg;
}

.quick-questions h3 {
  color: $lingnan-red;
  margin-bottom: $spacing-md;
  font-family: $font-family-serif;
}

.quick-question-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.quick-question-btn {
  background: linear-gradient(135deg, $deep-jade, $jade-green);
  color: $ivory;
  border: 2px solid $golden-yellow;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.4s ease;
  font-family: $font-family-traditional;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 1px 2px rgba(255,255,255,0.2),
    0 3px 6px rgba(0,0,0,0.2);

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba($golden-yellow, 0.15) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.4s ease;
  }

  &:hover::before {
    transform: scale(1);
  }

  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, $golden-yellow, $amber, $golden-yellow);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.6;
  }
}

.quick-question-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    inset 0 1px 2px rgba(255,255,255,0.3),
    0 8px 16px rgba($deep-jade, 0.4);
  border-color: $amber;
  background: linear-gradient(135deg, $jade-green, $bamboo-green);
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
}

.message-avatar {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.message-text {
  flex: 1;
  line-height: 1.6;
  font-size: 1rem;
  font-weight: 500;
  color: inherit;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-features {
  display: flex;
  justify-content: space-around;
  margin-top: $spacing-lg;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.feature-icon {
  font-size: 1.5rem;
}

.feature-text {
  font-size: 0.9rem;
  color: $text-secondary;
  font-family: $font-family-serif;
}

@media (max-width: 768px) {
  .quick-question-buttons {
    justify-content: center;
  }
  
  .chat-features {
    flex-wrap: wrap;
    gap: $spacing-md;
  }
  
  .feature-item {
    flex: 1;
    min-width: 100px;
  }
}
</style>