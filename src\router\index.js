import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Embroidery from '@/views/Embroidery.vue'
import Chat from '@/views/Chat.vue'
import About from '@/views/About.vue'
import StyleShowcase from '@/views/StyleShowcase.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/embroidery',
    name: 'Embroidery',
    component: Embroidery
  },
  {
    path: '/chat',
    name: 'Chat',
    component: Chat
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/style-showcase',
    name: 'StyleShowcase',
    component: StyleShowcase
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router