<template>
  <div class="style-showcase">
    <div class="showcase-header ruyi-cloud lingnan-brocade">
      <div class="header-content">
        <div class="main-title-seal">
          <h1 class="showcase-title traditional-animate">传统岭南广府风格展示</h1>
          <div class="title-seal">雅</div>
        </div>
        <p class="showcase-subtitle">Traditional Lingnan Guangfu Style UI Showcase</p>
        <div class="ornament-line">
          <span class="ornament ornament-animate">❋</span>
          <span class="divider shimmer-effect">◆◇◆◇◆</span>
          <span class="ornament ornament-animate">❋</span>
        </div>
      </div>
    </div>

    <div class="showcase-content">
      <!-- 色彩展示 -->
      <section class="color-section lingnan-card corner-ornaments">
        <h2 class="section-title">传统色彩 Traditional Colors</h2>
        <div class="color-palette">
          <div class="color-item">
            <div class="color-swatch vermillion-bg"></div>
            <span>朱红 Vermillion</span>
          </div>
          <div class="color-item">
            <div class="color-swatch golden-bg"></div>
            <span>金黄 Golden</span>
          </div>
          <div class="color-item">
            <div class="color-swatch jade-bg"></div>
            <span>翠绿 Jade</span>
          </div>
          <div class="color-item">
            <div class="color-swatch porcelain-bg"></div>
            <span>青花 Porcelain</span>
          </div>
          <div class="color-item">
            <div class="color-swatch ivory-bg"></div>
            <span>象牙 Ivory</span>
          </div>
        </div>
      </section>

      <!-- 纹样展示 -->
      <section class="pattern-section">
        <h2 class="section-title">传统纹样 Traditional Patterns</h2>
        <div class="pattern-grid">
          <div class="pattern-demo guangfu-window">
            <h3>广府窗棂纹</h3>
            <p>Guangfu Window Pattern</p>
          </div>
          <div class="pattern-demo wan-pattern">
            <h3>万字纹</h3>
            <p>Wan Pattern</p>
          </div>
          <div class="pattern-demo ruyi-cloud">
            <h3>如意云纹</h3>
            <p>Ruyi Cloud Pattern</p>
          </div>
          <div class="pattern-demo lingnan-brocade">
            <h3>岭南锦纹</h3>
            <p>Lingnan Brocade</p>
          </div>
        </div>
      </section>

      <!-- 印章元素展示 -->
      <section class="seal-section lingnan-card">
        <h2 class="section-title">印章元素 Seal Elements</h2>
        <div class="seal-gallery">
          <div class="demo-seal seal-animate">
            <span class="seal-text">瑶</span>
          </div>
          <div class="demo-seal seal-animate">
            <span class="seal-text">绣</span>
          </div>
          <div class="demo-seal seal-animate">
            <span class="seal-text">雅</span>
          </div>
          <div class="demo-seal seal-animate">
            <span class="seal-text">韵</span>
          </div>
        </div>
      </section>

      <!-- 按钮样式展示 -->
      <section class="button-section">
        <h2 class="section-title">按钮样式 Button Styles</h2>
        <div class="button-gallery">
          <button class="seal-button">印章按钮</button>
          <button class="lingnan-btn">岭南按钮</button>
          <button class="traditional-btn">传统按钮</button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.style-showcase {
  min-height: 100vh;
  background: 
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($rice-paper, 0.9)),
    radial-gradient(circle at 30% 30%, rgba($golden-yellow, 0.05) 0%, transparent 50%);
}

.showcase-header {
  padding: 60px 20px;
  text-align: center;
  border-bottom: 4px solid $bronze-gold;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.main-title-seal {
  position: relative;
  display: inline-block;
  padding: 25px 40px;
  background: 
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($pearl-white, 0.9)),
    radial-gradient(circle at center, rgba($golden-yellow, 0.15) 0%, transparent 70%);
  border: 4px solid $bronze-gold;
  border-radius: 15px;
  box-shadow: 
    inset 0 3px 6px rgba($golden-yellow, 0.3),
    0 6px 12px rgba(0,0,0,0.2);
  margin-bottom: 20px;
}

.showcase-title {
  font-family: $font-family-traditional;
  font-size: 2.5rem;
  color: $vermillion;
  margin: 0;
  font-weight: 700;
  letter-spacing: 3px;
}

.title-seal {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $ivory;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  font-family: $font-family-traditional;
  box-shadow: 0 3px 6px rgba(0,0,0,0.3);
  border: 2px solid $golden-yellow;
}

.showcase-subtitle {
  font-family: $font-family-serif;
  color: $text-secondary;
  font-size: 1.2rem;
  margin-bottom: 25px;
  font-style: italic;
}

.ornament-line {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.ornament {
  color: $jade-green;
  font-size: 1.5rem;
}

.divider {
  color: $golden-yellow;
  font-size: 1.2rem;
  letter-spacing: 3px;
}

.showcase-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.section-title {
  font-family: $font-family-traditional;
  font-size: 2rem;
  color: $vermillion;
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  
  &::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, $vermillion, $golden-yellow, $deep-jade);
    margin: 15px auto 0;
    border-radius: 2px;
  }
}

.color-section {
  margin-bottom: 50px;
}

.color-palette {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.color-item {
  text-align: center;
  font-family: $font-family-serif;
}

.color-swatch {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  border: 3px solid $golden-yellow;
}

.vermillion-bg { background: linear-gradient(135deg, $vermillion, $coral-red); }
.golden-bg { background: linear-gradient(135deg, $golden-yellow, $amber); }
.jade-bg { background: linear-gradient(135deg, $deep-jade, $jade-green); }
.porcelain-bg { background: linear-gradient(135deg, $porcelain-blue, $celadon); }
.ivory-bg { background: linear-gradient(135deg, $ivory, $pearl-white); }

.pattern-section {
  margin-bottom: 50px;
}

.pattern-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.pattern-demo {
  height: 150px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px solid $bronze-gold;
  background: rgba($ivory, 0.8);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  }
  
  h3 {
    font-family: $font-family-traditional;
    color: $vermillion;
    margin-bottom: 5px;
  }
  
  p {
    color: $text-secondary;
    font-style: italic;
  }
}

.seal-section {
  margin-bottom: 50px;
}

.seal-gallery {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.demo-seal {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $ivory;
  border: 3px solid $golden-yellow;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: $font-family-traditional;
  font-size: 1.8rem;
  font-weight: bold;
  box-shadow: 
    inset 0 2px 4px rgba(255,255,255,0.2),
    0 6px 12px rgba(0,0,0,0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

.button-section {
  margin-bottom: 50px;
}

.button-gallery {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.traditional-btn {
  background: linear-gradient(135deg, $deep-jade, $jade-green);
  color: $ivory;
  border: 2px solid $golden-yellow;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.4s ease;
  font-family: $font-family-traditional;
  font-weight: 500;
  box-shadow:
    inset 0 1px 2px rgba(255,255,255,0.2),
    0 3px 6px rgba(0,0,0,0.2);

  &:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      inset 0 1px 2px rgba(255,255,255,0.3),
      0 8px 16px rgba($deep-jade, 0.4);
    background: linear-gradient(135deg, $jade-green, $bamboo-green);
  }
}

@media (max-width: 768px) {
  .showcase-title {
    font-size: 2rem;
  }

  .color-palette,
  .seal-gallery,
  .button-gallery {
    gap: 20px;
  }

  .pattern-grid {
    grid-template-columns: 1fr;
  }

  .main-title-seal {
    padding: 20px 25px;
  }

  .ornament-line {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
