<template>
  <div class="embroidery-page">
    <div class="page-header">
      <h1 class="page-title">瑶绣展示</h1>
      <p class="page-subtitle">欣赏精美的岭南瑶绣作品，感受传统工艺的魅力</p>
    </div>
    
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-group">
          <label>分类筛选：</label>
          <select v-model="selectedCategory" class="filter-select">
            <option value="">全部分类</option>
            <option value="clothing">服饰类</option>
            <option value="decoration">装饰类</option>
            <option value="accessories">配饰类</option>
            <option value="art">艺术品类</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label>搜索：</label>
          <input 
            v-model="searchKeyword" 
            type="text" 
            placeholder="搜索作品名称或纹样..." 
            class="search-input"
          />
        </div>
        
        <button @click="resetFilters" class="reset-btn">
          重置筛选
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载瑶绣作品...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <div class="error-icon">⚠️</div>
      <p>{{ error }}</p>
    </div>

    <div v-if="!loading" class="embroidery-grid">
      <div 
        v-for="item in filteredEmbroidery" 
        :key="item.id"
        class="embroidery-card"
        @click="showDetail(item)"
      >
        <div class="card-image">
          <img
            v-if="item.thumbnailUrl || item.imageUrl"
            :src="item.thumbnailUrl || item.imageUrl"
            :alt="item.name"
            class="embroidery-image"
            @error="handleImageError"
          />
          <div v-else class="image-placeholder">
            <span class="placeholder-icon">🎨</span>
            <span class="placeholder-text">{{ item.name }}</span>
          </div>
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ item.name }}</h3>
          <p class="card-category">{{ item.category }}</p>
          <p class="card-description">{{ item.description }}</p>
          <div class="card-tags">
            <span 
              v-for="tag in item.tags" 
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="filteredEmbroidery.length === 0" class="no-results">
      <div class="no-results-icon">🔍</div>
      <h3>暂无相关作品</h3>
      <p>请尝试调整筛选条件或搜索关键词</p>
    </div>
    
    <!-- 详情弹窗 -->
    <div v-if="selectedItem" class="modal-overlay" @click="closeDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ selectedItem.name }}</h2>
          <button @click="closeDetail" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="detail-image">
            <img
              v-if="selectedItem.imageUrl"
              :src="selectedItem.imageUrl"
              :alt="selectedItem.name"
              class="detail-embroidery-image"
              @error="handleImageError"
            />
            <div v-else class="image-placeholder large">
              <span class="placeholder-icon">🎨</span>
              <span class="placeholder-text">{{ selectedItem.name }}</span>
            </div>
          </div>
          <div class="detail-info">
            <div class="info-item">
              <strong>分类：</strong>{{ selectedItem.category }}
            </div>
            <div class="info-item">
              <strong>年代：</strong>{{ selectedItem.era }}
            </div>
            <div class="info-item">
              <strong>尺寸：</strong>{{ selectedItem.size }}
            </div>
            <div class="info-item">
              <strong>工艺：</strong>{{ selectedItem.technique }}
            </div>
            <div class="info-item">
              <strong>纹样寓意：</strong>{{ selectedItem.meaning }}
            </div>
            <div class="info-item">
              <strong>详细介绍：</strong>
              <p>{{ selectedItem.fullDescription }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { embroideryApi, imageApi } from '@/api'

const selectedCategory = ref('')
const searchKeyword = ref('')
const selectedItem = ref(null)
const loading = ref(false)
const images = ref([])
const error = ref('')

// 加载图片数据
const loadImages = async () => {
  try {
    loading.value = true
    error.value = ''

    // 获取瑶绣相关图片
    const response = await imageApi.getImagesByCategory('瑶绣')
    if (response.data && response.data.images) {
      images.value = response.data.images
      // 将图片数据转换为展示格式
      embroideryData.value = images.value.map(img => ({
        id: img.id,
        name: img.title || img.filename || `瑶绣作品 ${img.id}`,
        category: img.category || '未分类',
        description: img.description || '精美的瑶绣作品',
        tags: img.tags || [],
        era: img.era || '现代',
        size: img.size || '未知',
        technique: img.technique || '传统瑶绣工艺',
        meaning: img.meaning || '传承文化之美',
        fullDescription: img.full_description || img.description || '这是一件精美的瑶绣作品，展现了传统工艺的魅力。',
        imageUrl: img.url,
        thumbnailUrl: img.thumbnail_url || img.url
      }))
    } else {
      // 如果API没有返回数据，使用默认数据
      loadDefaultData()
    }
  } catch (err) {
    console.error('加载图片失败:', err)
    error.value = '加载图片失败，使用默认数据'
    // 加载失败时使用默认数据
    loadDefaultData()
  } finally {
    loading.value = false
  }
}

// 默认数据
const loadDefaultData = () => {
  embroideryData.value = [
    {
      id: 1,
      name: '瑶族传统服饰',
      category: '服饰类',
      description: '精美的瑶族传统女装，采用传统瑶绣工艺制作',
      tags: ['传统', '服饰', '民族风'],
      era: '清代',
      size: '衣长120cm，胸围90cm',
      technique: '平绣、打籽绣',
      meaning: '寓意吉祥如意，生活美满',
      fullDescription: '这件瑶族传统服饰是典型的清代作品，采用多种传统针法制作。纹样包括花卉、鸟兽等，色彩鲜艳，寓意深远。是瑶族文化的重要载体。',
      imageUrl: null,
      thumbnailUrl: null
    },
    {
      id: 2,
      name: '花鸟纹样挂饰',
      category: '装饰类',
      description: '传统花鸟纹样瑶绣挂饰，寓意吉祥',
      tags: ['花鸟', '挂饰', '吉祥'],
      era: '民国时期',
      size: '60cm × 80cm',
      technique: '平绣、盘金绣',
      meaning: '花鸟象征着春天和生机',
      fullDescription: '这件花鸟纹样挂饰展现了瑶绣的精湛工艺，采用平绣和盘金绣相结合的技法。色彩搭配和谐，纹样生动活泼。',
      imageUrl: null,
      thumbnailUrl: null
    },
    {
      id: 3,
      name: '瑶绣手提包',
      category: '配饰类',
      description: '现代设计与传统工艺结合的瑶绣手提包',
      tags: ['现代', '手提包', '创新'],
      era: '现代',
      size: '25cm × 20cm × 10cm',
      technique: '平绣、十字绣',
      meaning: '传统与现代的完美结合',
      fullDescription: '这款手提包将传统瑶绣工艺与现代设计理念相结合，既有传统韵味，又符合现代审美。是瑶绣创新的典范。',
      imageUrl: null,
      thumbnailUrl: null
    },
    {
      id: 4,
      name: '龙凤呈祥壁挂',
      category: '艺术品类',
      description: '龙凤呈祥主题的大型瑶绣壁挂',
      tags: ['龙凤', '壁挂', '吉祥'],
      era: '现代',
      size: '150cm × 200cm',
      technique: '多种针法综合运用',
      meaning: '龙凤呈祥，寓意国泰民安',
      fullDescription: '这件大型壁挂作品以龙凤呈祥为主题，综合运用了多种传统针法。构图大气磅礴，色彩富丽堂皇，展现了瑶绣艺术的最高水准。',
      imageUrl: null,
      thumbnailUrl: null
    },
    {
      id: 5,
      name: '瑶绣围巾',
      category: '配饰类',
      description: '精致的瑶绣围巾，适合日常佩戴',
      tags: ['围巾', '日常', '实用'],
      era: '现代',
      size: '180cm × 30cm',
      technique: '平绣、链绣',
      meaning: '温暖与美的结合',
      fullDescription: '这款围巾采用优质面料，结合传统瑶绣工艺制作。图案精美，既实用又具有艺术价值，是现代生活中的理想配饰。',
      imageUrl: null,
      thumbnailUrl: null
    },
    {
      id: 6,
      name: '山水风景屏风',
      category: '装饰类',
      description: '山水风景主题的瑶绣屏风',
      tags: ['山水', '屏风', '风景'],
      era: '民国时期',
      size: '120cm × 180cm',
      technique: '平绣、打籽绣、盘金绣',
      meaning: '山水如画，寓意生活美好',
      fullDescription: '这件屏风作品以山水风景为主题，展现了瑶绣在大型作品创作上的卓越能力。针法细腻，色彩层次丰富，具有很高的艺术价值。',
      imageUrl: null,
      thumbnailUrl: null
    }
  ]
}

const embroideryData = ref([])

const filteredEmbroidery = computed(() => {
  let filtered = embroideryData.value
  
  if (selectedCategory.value) {
    filtered = filtered.filter(item => item.category === selectedCategory.value)
  }
  
  if (searchKeyword.value) {
    filtered = filtered.filter(item => 
      item.name.includes(searchKeyword.value) ||
      item.description.includes(searchKeyword.value) ||
      item.tags.some(tag => tag.includes(searchKeyword.value))
    )
  }
  
  return filtered
})

const resetFilters = () => {
  selectedCategory.value = ''
  searchKeyword.value = ''
}

const showDetail = (item) => {
  selectedItem.value = item
}

const closeDetail = () => {
  selectedItem.value = null
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  // 可以设置默认图片或隐藏图片
  event.target.style.display = 'none'
}

// 组件挂载时加载数据
onMounted(() => {
  loadImages()
})
</script>

<style scoped>
.embroidery-page {
  min-height: 100vh;
  padding: 40px 20px;
  background: 
    linear-gradient(135deg, rgba($pearl-white, 0.9), rgba($rice-paper, 0.8)),
    radial-gradient(circle at 25% 25%, rgba($jade-green, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba($porcelain-blue, 0.03) 0%, transparent 50%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 30% 40%, rgba($cinnabar-red, 0.02) 20%, transparent 21%),
      radial-gradient(circle at 70% 60%, rgba($imperial-yellow, 0.02) 15%, transparent 16%);
    background-size: 80px 80px, 60px 60px;
    pointer-events: none;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-family: $font-family-traditional;
  font-size: 2.8rem;
  color: $cinnabar-red;
  margin-bottom: 15px;
  text-shadow: 
    2px 2px 4px rgba(0,0,0,0.1),
    0 0 15px rgba($imperial-yellow, 0.3);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, $cinnabar-red, $imperial-yellow, $jade-green);
    border-radius: 2px;
  }
}

.page-subtitle {
  font-family: $font-family-serif;
  color: $text-secondary;
  font-size: 1.2rem;
}

.filter-section {
  margin-bottom: 40px;
}

.filter-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 600;
  color: $text-primary;
}

.filter-select,
.search-input {
  padding: 8px 12px;
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.filter-select:focus,
.search-input:focus {
  outline: none;
  border-color: $lingnan-red;
}

.search-input {
  width: 200px;
}

.reset-btn {
  background: $lingnan-red;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: background 0.3s ease;
}

.reset-btn:hover {
  background: $traditional-red;
}

.embroidery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.embroidery-card {
  background: 
    linear-gradient(135deg, rgba($pearl-white, 0.95), rgba($rice-paper, 0.9)),
    linear-gradient(45deg, transparent 45%, rgba($bronze-gold, 0.1) 45%, rgba($bronze-gold, 0.1) 55%, transparent 55%);
  background-size: 100% 100%, 20px 20px;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: 
    $shadow-md,
    inset 0 1px 0 rgba(255,255,255,0.5);
  transition: all 0.4s ease;
  cursor: pointer;
  border: 2px solid rgba($guangfu-gold, 0.3);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $cinnabar-red, $imperial-yellow, $jade-green, $porcelain-blue);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
}

.embroidery-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 12px 24px rgba(0,0,0,0.15),
    0 0 20px rgba($jade-green, 0.2);
  border-color: $imperial-yellow;
  background-size: 100% 100%, 15px 15px;
}

.card-image {
  height: 200px;
  background: linear-gradient(135deg, $lingnan-green, $traditional-jade);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.embroidery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.embroidery-card:hover .embroidery-image {
  transform: scale(1.05);
}

.image-placeholder {
  text-align: center;
  color: white;
}

.placeholder-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
}

.placeholder-text {
  font-size: 1.1rem;
  font-weight: 600;
}

.card-content {
  padding: 20px;
}

.card-title {
  font-family: $font-family-serif;
  color: $lingnan-red;
  font-size: 1.3rem;
  margin-bottom: 5px;
}

.card-category {
  color: $text-secondary;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.card-description {
  color: $text-primary;
  line-height: 1.5;
  margin-bottom: 15px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background: $lingnan-beige;
  color: $lingnan-red;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: $text-secondary;
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-results h3 {
  font-family: $font-family-serif;
  color: $lingnan-red;
  margin-bottom: 10px;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
  color: $text-secondary;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba($lingnan-red, 0.2);
  border-left: 4px solid $lingnan-red;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 40px 20px;
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  border-radius: $border-radius-md;
  margin: 20px auto;
  max-width: 600px;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid $border-color;
}

.modal-header h2 {
  font-family: $font-family-serif;
  color: $lingnan-red;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: $text-secondary;
  padding: 5px;
}

.close-btn:hover {
  color: $lingnan-red;
}

.modal-body {
  padding: 20px;
}

.detail-image {
  margin-bottom: 20px;
  text-align: center;
}

.detail-embroidery-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: $border-radius-md;
  box-shadow: $shadow-md;
}

.image-placeholder.large {
  height: 300px;
}

.detail-info {
  display: grid;
  gap: 15px;
}

.info-item {
  line-height: 1.6;
}

.info-item strong {
  color: $lingnan-red;
}

.info-item p {
  margin: 5px 0 0 0;
  color: $text-secondary;
}

@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .search-input {
    width: 100%;
  }
  
  .embroidery-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 10px;
  }
}
</style>