# 岭南瑶绣网站 - 传统岭南广府风格UI使用指南

## 概述

本指南介绍了优化后的岭南瑶绣网站UI系统，包含丰富的传统岭南广府风格元素，为开发者提供了一套完整的传统文化主题UI组件库。

## 快速开始

### 1. 启动项目
```bash
npm run dev
```

### 2. 访问页面
- 主页: http://localhost:3005/
- 风格展示页: http://localhost:3005/style-showcase
- 智能问答: http://localhost:3005/chat
- 瑶绣展示: http://localhost:3005/embroidery

## 核心样式类

### 传统纹样类

#### 背景纹样
```css
.lingnan-brocade        /* 岭南锦纹 - 多层径向渐变 */
.guangfu-window         /* 广府窗棂纹 - 交叉线性渐变 */
.wan-pattern           /* 万字纹 - 重复几何图案 */
.ruyi-cloud            /* 如意云纹 - 椭圆径向渐变 */
.xiangyun-pattern      /* 祥云纹 - 原有样式增强 */
.huiwen-pattern        /* 回纹 - 传统几何纹样 */
```

#### 使用示例
```html
<div class="content-area lingnan-brocade guangfu-window">
  <!-- 内容区域，同时应用锦纹和窗棂纹 -->
</div>
```

### 装饰元素类

#### 边框和角饰
```css
.corner-ornaments      /* 四角装饰 */
.traditional-border    /* 传统边框 */
.ruyi-border          /* 如意边框 */
.ruyi-divider         /* 如意分隔线 */
```

#### 印章元素
```css
.seal-button          /* 印章风格按钮 */
.seal-animate         /* 印章动画效果 */
```

### 动画效果类

#### 传统动画
```css
.traditional-animate   /* 传统光晕动画 */
.ornament-animate     /* 装饰元素浮动动画 */
.shimmer-effect       /* 金属闪烁效果 */
```

#### 使用示例
```html
<h1 class="title traditional-animate">标题</h1>
<div class="ornament ornament-animate">❋</div>
```

### 卡片和容器类

#### 岭南风格卡片
```css
.lingnan-card         /* 岭南风格卡片容器 */
.lingnan-title        /* 岭南风格标题 */
```

#### 使用示例
```html
<div class="lingnan-card corner-ornaments">
  <h2 class="lingnan-title">卡片标题</h2>
  <p>卡片内容</p>
</div>
```

## 色彩系统

### 主色调
```scss
$vermillion: #E25822;      // 朱红
$golden-yellow: #FFB347;   // 金黄
$deep-jade: #4A7C59;       // 深翠绿
$ivory: #FFFFF0;           // 象牙白
```

### 辅助色
```scss
$coral-red: #FF6B6B;       // 珊瑚红
$amber: #FFBF00;           // 琥珀色
$jade-green: #7FB069;      // 翡翠绿
$celadon: #87CEEB;         // 青瓷色
```

### 使用建议
- **主要内容**: 使用朱红和金黄突出重点
- **背景区域**: 使用象牙白和米纸色保持温润
- **装饰元素**: 使用翠绿和青瓷色增加层次

## 组件使用指南

### 1. 印章式标题
```html
<div class="main-title-seal">
  <h1 class="title traditional-animate">标题文字</h1>
  <div class="title-seal">印</div>
</div>
```

### 2. 传统按钮
```html
<button class="seal-button">印章按钮</button>
<button class="lingnan-btn">岭南按钮</button>
```

### 3. 装饰性印章
```html
<div class="demo-seal seal-animate">
  <span class="seal-text">瑶</span>
</div>
```

### 4. 纹样背景容器
```html
<section class="content-section ruyi-cloud lingnan-brocade">
  <div class="section-content">
    <!-- 内容 -->
  </div>
</section>
```

## 响应式设计

### 断点设置
- 移动端: `max-width: 768px`
- 平板端: `768px - 1024px`
- 桌面端: `min-width: 1024px`

### 移动端优化
```css
@media (max-width: 768px) {
  .hero-seal-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .side-seals {
    flex-direction: row;
    gap: 20px;
  }
}
```

## 最佳实践

### 1. 层次搭配
- 不要在同一元素上应用过多纹样
- 合理使用透明度控制纹样强度
- 保持内容的可读性

### 2. 色彩搭配
- 主色调不超过3种
- 保持足够的对比度
- 使用渐变增加层次感

### 3. 动画使用
- 动画时长控制在2-6秒
- 使用`ease-in-out`缓动函数
- 避免过于频繁的动画

### 4. 印章元素
- 印章文字保持简洁（1-2个字符）
- 合理控制印章大小和位置
- 保持印章的传统比例

## 自定义扩展

### 添加新纹样
```scss
.custom-pattern {
  background-image:
    // 自定义渐变或图案
    radial-gradient(circle at center, rgba($custom-color, 0.1) 0%, transparent 50%);
  background-size: 40px 40px;
}
```

### 创建新印章
```scss
.custom-seal {
  @extend .demo-seal;
  background: linear-gradient(135deg, $custom-color1, $custom-color2);
  // 其他自定义样式
}
```

## 注意事项

1. **性能考虑**: 复杂纹样可能影响渲染性能，建议在移动端适当简化
2. **浏览器兼容**: 部分CSS特性需要现代浏览器支持
3. **文化准确性**: 使用传统元素时要注意文化内涵的准确性
4. **可访问性**: 确保足够的颜色对比度，支持屏幕阅读器

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 完整的传统岭南广府风格UI系统
- 包含纹样、色彩、动画、组件等完整元素

---

如有问题或建议，请参考项目文档或联系开发团队。
