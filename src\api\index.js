import axios from 'axios'

const isMock = (import.meta.env.VITE_USE_MOCK ?? 'false') === 'true'

const resolvedBaseUrl = (() => {
  // 开发模式一律走 Vite 代理，避免跨域与端口差异
  if (import.meta.env.DEV) return '/api'
  // 生产环境：优先使用 VITE_API_URL，其次相对路径（需服务端同源提供 /api）
  const apiUrl = import.meta.env.VITE_API_URL
  if (apiUrl && typeof apiUrl === 'string') {
    return apiUrl.replace(/\/$/, '') + '/api'
  }
  return '/api'
})()

const api = axios.create({
  baseURL: resolvedBaseUrl,
  timeout: Number.parseInt(import.meta.env.VITE_API_TIMEOUT_MS || '60000', 10),
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API请求失败:', error)
    return Promise.reject(error)
  }
)

export const chatApi = {
  // 发送消息到智能体
  sendMessage: (message) => {
    if (isMock) {
      const reply = generateMockReply(message)
      mockHistory.push({
        id: Date.now(),
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      })
      mockHistory.push({
        id: Date.now() + 1,
        role: 'assistant',
        content: reply,
        timestamp: new Date().toISOString()
      })
      return Promise.resolve({ data: { reply } })
    }
    // 对接后端：默认 /qa/ask，可通过 VITE_CHAT_PATH 覆盖为 /chat/send 等
    const chatPath = (import.meta.env.VITE_CHAT_PATH || '/qa/ask').trim() || '/qa/ask'
    const payload = chatPath.endsWith('/chat/send') ? { message } : { question: message }
    return api.post(chatPath, payload)
      .then((res) => {
        const data = res?.data || {}
        // 兼容多种后端响应格式
        const reply =
          (data?.data && typeof data.data.answer === 'string' && data.data.answer) ||
          (typeof data.answer === 'string' && data.answer) ||
          (typeof data.reply === 'string' && data.reply) ||
          '后端未返回 answer 字段'
        return { data: { reply } }
      })
  },
  
  // 获取聊天历史
  getChatHistory: () => {
    if (isMock) {
      return Promise.resolve({ data: { history: mockHistory } })
    }
    // 后端未提供此接口，返回空结构避免 404
    return Promise.resolve({ data: { history: [] } })
  },
  
  // 清空聊天历史
  clearChatHistory: () => {
    if (isMock) {
      mockHistory.length = 0
      return Promise.resolve({ data: { success: true } })
    }
    // 后端未提供此接口，直接返回成功
    return Promise.resolve({ data: { success: true } })
  }
}

export const embroideryApi = {
  // 获取瑶绣作品列表
  getEmbroideryList: (params = {}) => {
    return api.get('/embroidery/list', { params })
  },

  // 获取瑶绣作品详情
  getEmbroideryDetail: (id) => {
    return api.get(`/embroidery/${id}`)
  },

  // 获取瑶绣分类
  getCategories: () => {
    return api.get('/embroidery/categories')
  },

  // 搜索瑶绣作品
  searchEmbroidery: (keyword) => {
    return api.get('/embroidery/search', { params: { keyword } })
  }
}

export const imageApi = {
  // 获取图片列表
  getImageList: (params = {}) => {
    return api.get('/images', { params })
  },

  // 根据ID获取图片详情
  getImageById: (id) => {
    return api.get(`/images/${id}`)
  },

  // 根据标签获取图片
  getImagesByTag: (tag) => {
    return api.get('/images/tag', { params: { tag } })
  },

  // 根据分类获取图片
  getImagesByCategory: (category) => {
    return api.get('/images/category', { params: { category } })
  },

  // 搜索图片
  searchImages: (keyword) => {
    return api.get('/images/search', { params: { keyword } })
  },

  // 获取随机图片
  getRandomImages: (count = 10) => {
    return api.get('/images/random', { params: { count } })
  }
}

export const cultureApi = {
  // 获取文化知识
  getCultureKnowledge: (category) => {
    return api.get('/culture/knowledge', { params: { category } })
  },
  
  // 获取工艺技法
  getCraftTechniques: () => {
    return api.get('/culture/techniques')
  },
  
  // 获取历史渊源
  getHistory: () => {
    return api.get('/culture/history')
  }
}

export default api

// --------------------
// Mock 实现（仅在 VITE_USE_MOCK=true 时启用）
// --------------------
const mockHistory = []

function generateMockReply(input) {
  const trimmed = String(input || '').trim()
  if (!trimmed) return '您好，我是瑶绣智能助手，请描述您的问题。'
  const faqs = [
    {
      q: /历史|起源|源流/,
      a: '瑶绣历史渊源可追溯至先秦时期，技法在明清时期日趋成熟，具有浓郁的岭南地域特色与民族审美。'
    },
    {
      q: /纹样|图案|寓意/,
      a: '瑶绣常见纹样有凤凰、牡丹、回纹与几何纹，寓意吉祥、兴旺与守护。'
    },
    {
      q: /工艺|技法|针法/,
      a: '常用针法包括平针、抢针、套针与打籽等，讲究色彩层次与针脚细密。'
    },
    {
      q: /真伪|鉴别|辨别/,
      a: '鉴别要点包括丝线光泽、针脚疏密、背面收线与题识印章，手工绣多有细微不完美却富有生命力。'
    }
  ]
  const found = faqs.find(item => item.q.test(trimmed))
  return found ? found.a : `关于“${trimmed}”的问题，我可以从历史、工艺、纹样与文化意义等方面为您逐步解答。`
}