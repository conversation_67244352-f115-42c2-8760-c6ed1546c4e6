<template>
  <footer class="footer traditional-border guangfu-window corner-ornaments lingnan-brocade">
    <div class="footer-content">
      <div class="footer-section main-section">
        <div class="footer-seal">
          <h3>岭南瑶绣</h3>
          <div class="seal-decoration">印</div>
        </div>
        <p>传承千年工艺，弘扬民族文化，智能传承未来</p>
        <div class="cultural-symbols">
          <span class="symbol">❋</span>
          <span class="symbol">❀</span>
          <span class="symbol">❁</span>
          <span class="symbol">✿</span>
        </div>
        <div class="traditional-motto">
          <span class="motto-text">匠心独运 · 文化传承</span>
        </div>
      </div>
      
      <div class="footer-section">
        <h4>快速链接</h4>
        <ul class="footer-links">
          <li><router-link to="/">首页</router-link></li>
          <li><router-link to="/embroidery">瑶绣展示</router-link></li>
          <li><router-link to="/chat">智能问答</router-link></li>
          <li><router-link to="/about">关于我们</router-link></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>文化特色</h4>
        <ul class="footer-links">
          <li>传统纹样</li>
          <li>工艺技法</li>
          <li>历史传承</li>
          <li>现代创新</li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>联系我们</h4>
        <div class="contact-info">
          <p>📧 <EMAIL></p>
          <p>📞 +86 123-4567-8900</p>
          <p>📍 中国广东省广州市</p>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <div class="ruyi-divider"></div>
      <div class="footer-bottom-content">
        <p>&copy; 2024 岭南瑶绣展示平台. 保留所有权利.</p>
        <div class="traditional-pattern">❋ ❀ ❁ ❋ ❀ ❁</div>
      </div>
    </div>
  </footer>
</template>

<script setup>
</script>

<style scoped>
.footer {
  background:
    linear-gradient(135deg, rgba($ink-black, 0.92), rgba($guangfu-ink, 0.85)),
    radial-gradient(circle at 25% 25%, rgba($vermillion, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba($deep-jade, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba($golden-yellow, 0.08) 0%, transparent 60%);
  color: $ivory;
  margin-top: auto;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, $vermillion, $golden-yellow, $deep-jade, $porcelain-blue, $coral-red);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 50px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.main-section {
  grid-column: span 2;
}

.footer-seal {
  position: relative;
  display: inline-block;
  padding: 15px 25px;
  background:
    linear-gradient(135deg, rgba($ivory, 0.1), rgba($pearl-white, 0.05)),
    radial-gradient(circle at center, rgba($golden-yellow, 0.1) 0%, transparent 70%);
  border: 2px solid $bronze-gold;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow:
    inset 0 1px 2px rgba($golden-yellow, 0.2),
    0 3px 6px rgba(0,0,0,0.3);
}

.footer-section h3 {
  font-family: $font-family-traditional;
  color: $golden-yellow;
  font-size: 1.8rem;
  margin: 0;
  text-shadow:
    2px 2px 4px rgba(0,0,0,0.4),
    0 0 15px rgba($golden-yellow, 0.5);
  letter-spacing: 2px;
}

.seal-decoration {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, $vermillion, $coral-red);
  color: $ivory;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  font-family: $font-family-traditional;
  box-shadow: 0 2px 4px rgba(0,0,0,0.4);
  border: 1px solid $golden-yellow;
}

.footer-section h4 {
  font-family: $font-family-serif;
  color: $lingnan-beige;
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.footer-section p {
  color: $bg-secondary;
  line-height: 1.6;
  margin-bottom: 10px;
}

.traditional-motto {
  margin-top: 15px;
  padding: 10px 15px;
  background: rgba($golden-yellow, 0.1);
  border: 1px solid rgba($golden-yellow, 0.3);
  border-radius: 8px;
  text-align: center;
}

.motto-text {
  font-family: $font-family-traditional;
  color: $golden-yellow;
  font-size: 1rem;
  font-style: italic;
  letter-spacing: 1px;
}

.cultural-symbols {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  justify-content: center;
}

.symbol {
  font-size: 1.4rem;
  color: $golden-yellow;
  opacity: 0.8;
  animation: glow 3s ease-in-out infinite alternate;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.2);
    text-shadow: 0 0 10px rgba($golden-yellow, 0.8);
  }

  &:nth-child(2) {
    animation-delay: 0.5s;
    color: $vermillion;
  }

  &:nth-child(3) {
    animation-delay: 1s;
    color: $deep-jade;
  }

  &:nth-child(4) {
    animation-delay: 1.5s;
    color: $coral-red;
  }
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: $bg-secondary;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: $lingnan-gold;
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.footer-bottom {
  background: rgba(0, 0, 0, 0.2);
  padding: 20px 0;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: $bg-secondary;
  margin: 0;
}

.traditional-pattern {
  color: $lingnan-gold;
  font-size: 1rem;
  opacity: 0.6;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .cultural-symbols {
    justify-content: center;
  }
}
</style>