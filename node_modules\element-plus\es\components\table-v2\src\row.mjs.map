{"version": 3, "file": "row.mjs", "sources": ["../../../../../../packages/components/table-v2/src/row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { virtualizedGridProps } from '@element-plus/components/virtual-list'\nimport { columns, expandColumnKey, rowKey } from './common'\n\nimport type {\n  CSSProperties,\n  ExtractPropTypes,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { FixedDirection, KeyType, RowCommonParams } from './types'\n\nexport type RowExpandParams = {\n  expanded: boolean\n  rowKey: KeyType\n} & RowCommonParams\n\nexport type RowHoverParams = {\n  event?: MouseEvent\n  hovered: boolean\n  rowKey: KeyType\n} & Partial<RowCommonParams>\n\nexport type RowEventHandlerParams = {\n  rowKey: KeyType\n  event: Event\n} & RowCommonParams\n\nexport type RowHeightChangedParams = {\n  rowKey: KeyType\n  height: number\n  rowIndex: number\n}\n\nexport type RowExpandHandler = (params: RowExpandParams) => void\nexport type RowHoverHandler = (params: RowHoverParams) => void\nexport type RowEventHandler = (params: RowEventHandlerParams) => void\nexport type RowHeightChangeHandler = (\n  row: RowHeightChangedParams,\n  fixedDirection: boolean | FixedDirection | undefined\n) => void\n\nexport type RowEventHandlers = {\n  onClick?: RowEventHandler\n  onContextmenu?: RowEventHandler\n  onDblclick?: RowEventHandler\n  onMouseenter?: RowEventHandler\n  onMouseleave?: RowEventHandler\n}\n\nexport const tableV2RowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType<Record<KeyType, CSSProperties>>(Object),\n    required: true,\n  },\n  depth: Number,\n  expandColumnKey,\n  estimatedRowHeight: {\n    ...virtualizedGridProps.estimatedRowHeight,\n    default: undefined,\n  },\n  isScrolling: Boolean,\n  onRowExpand: {\n    type: definePropType<RowExpandHandler>(Function),\n  },\n  onRowHover: {\n    type: definePropType<RowHoverHandler>(Function),\n  },\n  onRowHeightChange: {\n    type: definePropType<RowHeightChangeHandler>(Function),\n  },\n  rowData: {\n    type: definePropType<any>(Object),\n    required: true,\n  },\n  rowEventHandlers: {\n    type: definePropType<RowEventHandlers>(Object),\n  },\n  rowIndex: {\n    type: Number,\n    required: true,\n  },\n  /**\n   * Unique item key\n   */\n  rowKey,\n  style: {\n    type: definePropType<CSSProperties>(Object),\n  },\n} as const)\n\nexport type TableV2RowProps = ExtractPropTypes<typeof tableV2RowProps>\nexport type TableV2RowPropsPublic = __ExtractPublicPropTypes<\n  typeof tableV2RowProps\n>\n"], "names": [], "mappings": ";;;;AAGY,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,OAAO;AACT,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,eAAe;AACjB,EAAE,kBAAkB,EAAE;AACtB,IAAI,GAAG,oBAAoB,CAAC,kBAAkB;AAC9C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,MAAM;AACR,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC;;;;"}