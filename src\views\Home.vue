<template>
  <div class="home">
    <div class="hero-section ruyi-cloud lingnan-brocade">
      <div class="hero-content">
        <div class="hero-seal-container">
          <div class="main-seal">
            <h1 class="hero-title">岭南瑶绣</h1>
            <div class="seal-border"></div>
          </div>
          <div class="side-seals">
            <div class="mini-seal">瑶</div>
            <div class="mini-seal">绣</div>
          </div>
        </div>
        <p class="hero-subtitle">千年传承 · 匠心独运 · 文化瑰宝 · 智能传承</p>
        <div class="subtitle-ornament">
          <span class="ornament">❋</span>
          <span class="divider">◆◇◆</span>
          <span class="ornament">❋</span>
        </div>
        <div class="hero-buttons">
          <router-link to="/embroidery" class="hero-btn primary">
            <span class="btn-icon">🎨</span>
            探索瑶绣
          </router-link>
          <router-link to="/chat" class="hero-btn secondary">
            <span class="btn-icon">🤖</span>
            智能问答
          </router-link>
        </div>
      </div>
      <div class="hero-decoration">
        <div class="decorative-circle main-circle"></div>
        <div class="decorative-circle secondary-circle"></div>
        <div class="traditional-pattern pattern-1">❋</div>
        <div class="traditional-pattern pattern-2">❀</div>
        <div class="traditional-pattern pattern-3">❁</div>
        <div class="traditional-pattern pattern-4">✿</div>
        <div class="floating-seal seal-1">瑶</div>
        <div class="floating-seal seal-2">绣</div>
      </div>
    </div>

    <div class="features-section">
      <div class="section-container">
        <h2 class="section-title">文化特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>纹样丰富</h3>
            <p>瑶绣纹样蕴含深厚的文化内涵，包括自然万物、神话传说、生活场景等</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🧵</div>
            <h3>工艺精湛</h3>
            <p>采用传统针法，色彩搭配独特，每一针每一线都凝聚着匠人的智慧</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3>历史悠久</h3>
            <p>瑶绣有着千年的历史传承，是瑶族文化的重要组成部分</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🌟</div>
            <h3>文化价值</h3>
            <p>2006年被列入国家级非物质文化遗产名录，具有重要的文化价值</p>
          </div>
        </div>
      </div>
    </div>

    <div class="showcase-section">
      <div class="section-container">
        <h2 class="section-title">精品展示</h2>
        <div class="showcase-grid">
          <div class="showcase-item">
            <div class="showcase-image">
              <div class="image-placeholder">瑶绣作品</div>
            </div>
            <h3>传统服饰</h3>
            <p>精美的瑶族传统服饰，展现独特的民族风情</p>
          </div>
          <div class="showcase-item">
            <div class="showcase-image">
              <div class="image-placeholder">瑶绣作品</div>
            </div>
            <h3>装饰用品</h3>
            <p>各类装饰性瑶绣作品，富有艺术价值</p>
          </div>
          <div class="showcase-item">
            <div class="showcase-image">
              <div class="image-placeholder">瑶绣作品</div>
            </div>
            <h3>现代创新</h3>
            <p>融合现代设计理念的瑶绣创新作品</p>
          </div>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="section-container">
        <h2 class="section-title">开始探索</h2>
        <p class="cta-description">
          通过我们的智能问答助手，深入了解瑶绣的文化内涵、工艺技法和发展历程
        </p>
        <router-link to="/chat" class="cta-button">
          开始问答
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.hero-section {
  background:
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($rice-paper, 0.9)),
    radial-gradient(circle at 30% 70%, rgba($deep-jade, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba($vermillion, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba($golden-yellow, 0.08) 0%, transparent 60%);
  padding: 100px 20px;
  position: relative;
  overflow: hidden;
  border-bottom: 4px solid $bronze-gold;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba($porcelain-blue, 0.05) 20%, transparent 21%),
    radial-gradient(circle at 75% 75%, rgba($jade-green, 0.05) 20%, transparent 21%);
  background-size: 60px 60px;
  pointer-events: none;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-seal-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 30px;
}

.main-seal {
  position: relative;
  padding: 25px 40px;
  background:
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($pearl-white, 0.9)),
    radial-gradient(circle at center, rgba($golden-yellow, 0.15) 0%, transparent 70%);
  border: 4px solid $bronze-gold;
  border-radius: 15px;
  box-shadow:
    inset 0 3px 6px rgba($golden-yellow, 0.3),
    0 6px 12px rgba(0,0,0,0.2),
    0 0 0 2px rgba($vermillion, 0.3);
}

.hero-title {
  font-family: $font-family-traditional;
  font-size: 4.2rem;
  color: $vermillion;
  margin: 0;
  font-weight: 700;
  text-shadow:
    3px 3px 6px rgba(0,0,0,0.15),
    0 0 25px rgba($golden-yellow, 0.4);
  letter-spacing: 4px;
  position: relative;
}

.seal-border {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 2px solid rgba($golden-yellow, 0.6);
  border-radius: 8px;
  pointer-events: none;
}

.side-seals {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mini-seal {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $ivory;
  border: 3px solid $golden-yellow;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: $font-family-traditional;
  font-size: 1.5rem;
  font-weight: bold;
  box-shadow:
    inset 0 2px 4px rgba(255,255,255,0.2),
    0 4px 8px rgba(0,0,0,0.3);
  animation: float 4s ease-in-out infinite;

  &:nth-child(2) {
    animation-delay: 2s;
  }
}

.hero-subtitle {
  font-family: $font-family-serif;
  font-size: 1.6rem;
  color: $text-secondary;
  margin-bottom: 20px;
  font-style: italic;
  letter-spacing: 2px;
}

.subtitle-ornament {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 40px;
}

.ornament {
  color: $jade-green;
  font-size: 1.5rem;
  animation: glow 3s ease-in-out infinite alternate;
}

.divider {
  color: $golden-yellow;
  font-size: 1.2rem;
  letter-spacing: 3px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 18px 45px;
  border-radius: $border-radius-lg;
  text-decoration: none;
  font-family: $font-family-traditional;
  font-size: 1.2rem;
  font-weight: 600;
  transition: all 0.4s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.btn-icon {
  font-size: 1.3rem;
}

.hero-btn.primary {
  background: linear-gradient(135deg, $vermillion, $coral-red);
  color: $ivory;
  box-shadow:
    $shadow-md,
    inset 0 2px 4px rgba(255,255,255,0.2),
    0 0 0 3px rgba($golden-yellow, 0.4);
  border: 2px solid $golden-yellow;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba($golden-yellow, 0.4), transparent);
    transition: left 0.6s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, $golden-yellow, $amber, $golden-yellow);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.7;
  }
}

.hero-btn.primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    $shadow-lg,
    0 0 25px rgba($vermillion, 0.5),
    0 0 0 4px rgba($golden-yellow, 0.6);
  background: linear-gradient(135deg, $coral-red, $vermillion);
}

.hero-btn.secondary {
  background:
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($pearl-white, 0.9)),
    radial-gradient(circle at center, rgba($deep-jade, 0.1) 0%, transparent 60%);
  color: $deep-jade;
  border: 2px solid $deep-jade;
  box-shadow:
    $shadow-sm,
    inset 0 1px 2px rgba(255,255,255,0.5);

  &::before {
    background: linear-gradient(90deg, transparent, rgba($deep-jade, 0.2), transparent);
  }
}

.hero-btn.secondary:hover {
  background: linear-gradient(135deg, $deep-jade, $jade-green);
  color: $ivory;
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    $shadow-lg,
    0 0 20px rgba($deep-jade, 0.4);
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.main-circle {
  position: absolute;
  width: 400px;
  height: 400px;
  border: 4px solid rgba($bronze-gold, 0.2);
  border-radius: 50%;
  top: -200px;
  right: -200px;
  background:
    radial-gradient(circle at 30% 30%, rgba($golden-yellow, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba($vermillion, 0.05) 0%, transparent 50%);
}

.secondary-circle {
  position: absolute;
  width: 250px;
  height: 250px;
  border: 3px solid rgba($deep-jade, 0.15);
  border-radius: 50%;
  bottom: -125px;
  left: -125px;
  background: radial-gradient(circle at center, rgba($jade-green, 0.08) 0%, transparent 60%);
}

.traditional-pattern {
  position: absolute;
  font-size: 2.5rem;
  animation: float 6s ease-in-out infinite;
  text-shadow: 0 0 10px rgba($golden-yellow, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.3);
    text-shadow: 0 0 20px rgba($golden-yellow, 0.6);
  }
}

.pattern-1 {
  top: 15%;
  left: 8%;
  color: rgba($vermillion, 0.3);
  animation-delay: 0s;
}

.pattern-2 {
  top: 65%;
  right: 12%;
  color: rgba($deep-jade, 0.3);
  animation-delay: 1.5s;
}

.pattern-3 {
  bottom: 25%;
  left: 15%;
  color: rgba($golden-yellow, 0.4);
  animation-delay: 3s;
}

.pattern-4 {
  top: 40%;
  right: 8%;
  color: rgba($coral-red, 0.3);
  animation-delay: 4.5s;
}

.floating-seal {
  position: absolute;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $ivory;
  border: 3px solid $golden-yellow;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: $font-family-traditional;
  font-size: 1.8rem;
  font-weight: bold;
  box-shadow:
    inset 0 2px 4px rgba(255,255,255,0.2),
    0 6px 12px rgba(0,0,0,0.3);
  animation: float 8s ease-in-out infinite;
  opacity: 0.8;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

.seal-1 {
  top: 30%;
  left: 5%;
  animation-delay: 1s;
}

.seal-2 {
  bottom: 30%;
  right: 5%;
  animation-delay: 5s;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-family: $font-family-serif;
  font-size: 2.5rem;
  color: $lingnan-red;
  text-align: center;
  margin-bottom: 60px;
  position: relative;
}

.section-title::after {
  content: '';
  display: block;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, $lingnan-red, $lingnan-gold);
  margin: 20px auto 0;
}

.features-section {
  padding: 80px 20px;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.feature-card {
  background: $bg-secondary;
  padding: 40px 30px;
  border-radius: $border-radius-lg;
  text-align: center;
  box-shadow: $shadow-sm;
  transition: all 0.3s ease;
  border: 1px solid $border-color;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: $shadow-md;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-family: $font-family-serif;
  color: $lingnan-red;
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.feature-card p {
  color: $text-secondary;
  line-height: 1.6;
}

.showcase-section {
  padding: 80px 20px;
  background: $lingnan-beige;
}

.showcase-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.showcase-item {
  background: white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-md;
  transition: all 0.3s ease;
}

.showcase-item:hover {
  transform: translateY(-5px);
  box-shadow: $shadow-lg;
}

.showcase-image {
  height: 200px;
  background: linear-gradient(135deg, $lingnan-green, $traditional-jade);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.showcase-item h3 {
  font-family: $font-family-serif;
  color: $lingnan-red;
  font-size: 1.3rem;
  margin: 20px 0 10px;
  padding: 0 20px;
}

.showcase-item p {
  color: $text-secondary;
  padding: 0 20px 20px;
  line-height: 1.6;
}

.cta-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, $lingnan-red, $traditional-red);
  color: white;
  text-align: center;
}

.cta-section .section-title {
  color: white;
}

.cta-description {
  font-size: 1.2rem;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  display: inline-block;
  background: white;
  color: $lingnan-red;
  padding: 15px 40px;
  border-radius: $border-radius-lg;
  text-decoration: none;
  font-family: $font-family-serif;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid,
  .showcase-grid {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2rem;
  }
}
</style>