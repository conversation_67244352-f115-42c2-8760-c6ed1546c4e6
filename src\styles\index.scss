* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: $font-family-sans;
  background-color: $rice-paper;
  color: $text-primary;
  line-height: 1.6;
}

// 岭南风格装饰
.lingnan-pattern {
  background-image:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(196, 30, 58, 0.05) 10px,
      rgba(196, 30, 58, 0.05) 20px
    );
}

// 传统岭南锦纹背景
.lingnan-brocade {
  background-image:
    radial-gradient(circle at 20% 20%, rgba($vermillion, 0.08) 15%, transparent 16%),
    radial-gradient(circle at 80% 80%, rgba($deep-jade, 0.08) 15%, transparent 16%),
    radial-gradient(circle at 40% 60%, rgba($golden-yellow, 0.06) 12%, transparent 13%),
    radial-gradient(circle at 60% 40%, rgba($coral-red, 0.06) 12%, transparent 13%);
  background-size: 80px 80px, 80px 80px, 60px 60px, 60px 60px;
  background-position: 0 0, 40px 40px, 20px 30px, 50px 10px;
}

// 广府传统窗棂纹
.guangfu-window {
  background-image:
    linear-gradient(0deg, rgba($bronze-gold, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba($bronze-gold, 0.2) 1px, transparent 1px),
    linear-gradient(45deg, rgba($bronze-gold, 0.1) 1px, transparent 1px),
    linear-gradient(-45deg, rgba($bronze-gold, 0.1) 1px, transparent 1px);
  background-size: 20px 20px, 20px 20px, 28px 28px, 28px 28px;
  background-position: 0 0, 0 0, 0 0, 0 0;
}

// 回纹装饰（传统几何纹样）
.huiwen-pattern {
  background-image:
    linear-gradient(90deg, $guangfu-gold 2px, transparent 2px),
    linear-gradient(0deg, $guangfu-gold 2px, transparent 2px),
    linear-gradient(90deg, transparent 6px, $guangfu-gold 6px, $guangfu-gold 8px, transparent 8px),
    linear-gradient(0deg, transparent 6px, $guangfu-gold 6px, $guangfu-gold 8px, transparent 8px);
  background-size: 12px 12px, 12px 12px, 24px 24px, 24px 24px;
  background-position: 0 0, 0 0, 0 0, 0 0;
  opacity: 0.15;
}

// 传统万字纹（卍字纹）
.wan-pattern {
  background-image:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 8px,
      rgba($bronze-gold, 0.12) 8px,
      rgba($bronze-gold, 0.12) 10px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 8px,
      rgba($bronze-gold, 0.12) 8px,
      rgba($bronze-gold, 0.12) 10px
    );
  background-size: 32px 32px;
}

// 传统如意云纹
.ruyi-cloud {
  background-image:
    radial-gradient(ellipse 30px 20px at 25% 40%, rgba($jade-green, 0.1) 40%, transparent 41%),
    radial-gradient(ellipse 25px 15px at 75% 60%, rgba($vermillion, 0.1) 40%, transparent 41%),
    radial-gradient(ellipse 20px 25px at 50% 20%, rgba($golden-yellow, 0.08) 40%, transparent 41%),
    radial-gradient(ellipse 35px 18px at 30% 80%, rgba($porcelain-blue, 0.08) 40%, transparent 41%);
  background-size: 100px 80px, 90px 70px, 80px 90px, 110px 75px;
  background-position: 0 0, 50px 40px, 25px 60px, 75px 20px;
}

// 祥云纹装饰
.xiangyun-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba($porcelain-blue, 0.1) 20%, transparent 21%),
    radial-gradient(circle at 75% 75%, rgba($jade-green, 0.1) 20%, transparent 21%),
    radial-gradient(circle at 75% 25%, rgba($cinnabar-red, 0.1) 15%, transparent 16%),
    radial-gradient(circle at 25% 75%, rgba($imperial-yellow, 0.1) 15%, transparent 16%);
  background-size: 40px 40px;
  background-position: 0 0, 0 0, 20px 20px, 20px 20px;
}

// 如意纹边框
.ruyi-border {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, $cinnabar-red, $imperial-yellow, $jade-green, $porcelain-blue);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.8;
  }
}

// 印章风格按钮
.seal-button {
  position: relative;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $pearl-white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-family: $font-family-traditional;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.2),
    0 4px 8px rgba(0,0,0,0.3),
    0 0 0 2px rgba($golden-yellow, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, $golden-yellow, $amber, $golden-yellow);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.6;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      inset 0 1px 0 rgba(255,255,255,0.3),
      0 6px 12px rgba(0,0,0,0.4),
      0 0 0 3px rgba($golden-yellow, 0.5);
    background: linear-gradient(135deg, $vermillion, $coral-red);
  }

  &:active {
    transform: translateY(0);
  }
}

// 广府花窗格纹（叠加，不覆盖底色）
.guangfu-lattice {
  background-image:
    linear-gradient(0deg, rgba($guangfu-gold, 0.14) 1px, transparent 1px),
    linear-gradient(90deg, rgba($guangfu-gold, 0.14) 1px, transparent 1px);
  background-size: 16px 16px;
  background-position: center;
}

// 如意分隔线（用于区块上下装饰）
.ruyi-divider {
  height: 12px;
  background:
    radial-gradient(circle at 8px 50%, rgba($guangfu-gold, .85) 2px, transparent 3px) 0 0/32px 100% repeat-x,
    linear-gradient(to right, transparent 0, rgba($guangfu-gold, .8) 20%, transparent 40%, rgba($guangfu-gold, .8) 60%, transparent 80%) 0 50%/100% 2px no-repeat;
  opacity: .8;
}

// 角纹装饰（不影响原背景，覆盖在最上层）
.corner-ornaments {
  position: relative;
}

.corner-ornaments::after {
  content: '';
  position: absolute;
  inset: 0;
  pointer-events: none;
  background:
    linear-gradient(0deg, $guangfu-gold, $guangfu-gold) top left/16px 2px no-repeat,
    linear-gradient(90deg, $guangfu-gold, $guangfu-gold) top left/2px 16px no-repeat,
    linear-gradient(0deg, $guangfu-gold, $guangfu-gold) top right/16px 2px no-repeat,
    linear-gradient(90deg, $guangfu-gold, $guangfu-gold) top right/2px 16px no-repeat,
    linear-gradient(0deg, $guangfu-gold, $guangfu-gold) bottom left/16px 2px no-repeat,
    linear-gradient(90deg, $guangfu-gold, $guangfu-gold) bottom left/2px 16px no-repeat,
    linear-gradient(0deg, $guangfu-gold, $guangfu-gold) bottom right/16px 2px no-repeat,
    linear-gradient(90deg, $guangfu-gold, $guangfu-gold) bottom right/2px 16px no-repeat;
  border-radius: inherit;
}

// 墨色标题风格
.ink-title {
  color: $guangfu-ink;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 0 rgba(0,0,0,0.05);
}

// 传统纹样装饰
.traditional-border {
  border: 2px solid $lingnan-red;
  border-image: repeating-linear-gradient(
    45deg,
    $lingnan-red,
    $lingnan-red 10px,
    $lingnan-gold 10px,
    $lingnan-gold 20px
  ) 1;
}

// 岭南风格按钮
.lingnan-btn {
  background: linear-gradient(135deg, $lingnan-red, $traditional-red);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: $border-radius-md;
  font-family: $font-family-serif;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: $shadow-md;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
    background: linear-gradient(135deg, $traditional-red, $lingnan-red);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 岭南风格卡片
.lingnan-card {
  background: 
    linear-gradient(135deg, rgba($pearl-white, 0.95), rgba($rice-paper, 0.9)),
    linear-gradient(45deg, transparent 48%, rgba($guangfu-gold, 0.1) 48%, rgba($guangfu-gold, 0.1) 52%, transparent 52%);
  background-size: 100% 100%, 24px 24px;
  border-radius: $border-radius-lg;
  padding: $spacing-xl $spacing-lg;
  box-shadow: 
    $shadow-md,
    inset 0 1px 0 rgba(255,255,255,0.6),
    inset 0 -1px 0 rgba($guangfu-gold, 0.2);
  border: 2px solid rgba($guangfu-gold, 0.4);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, $cinnabar-red, $imperial-yellow, $jade-green, $porcelain-blue);
    opacity: 0.9;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, $guangfu-gold, transparent);
    opacity: 0.6;
  }
}

// 岭南风格标题
.lingnan-title {
  font-family: $font-family-traditional;
  color: $cinnabar-red;
  font-size: 2.8rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: $spacing-lg;
  position: relative;
  text-shadow: 
    2px 2px 4px rgba(0,0,0,0.1),
    0 0 20px rgba($imperial-yellow, 0.3);
  letter-spacing: 2px;
  
  &::before {
    content: '❋';
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    color: $jade-green;
    font-size: 1.5rem;
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  &::after {
    content: '';
    display: block;
    width: 120px;
    height: 4px;
    background: linear-gradient(90deg, $cinnabar-red, $imperial-yellow, $jade-green, $porcelain-blue);
    margin: $spacing-md auto 0;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
}

// 智能体对话样式
.chat-container {
  max-width: 800px;
  margin: 0 auto;
  padding: $spacing-lg;
}

.chat-messages {
  height: 400px;
  overflow-y: auto;
  border: 2px solid $cinnabar-red;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  background: 
    linear-gradient(135deg, rgba($pearl-white, 0.95), rgba($rice-paper, 0.9)),
    linear-gradient(45deg, transparent 48%, rgba($guangfu-gold, 0.05) 48%, rgba($guangfu-gold, 0.05) 52%, transparent 52%);
  background-size: 100% 100%, 30px 30px;
  margin-bottom: $spacing-md;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.message {
  margin-bottom: $spacing-md;
  padding: $spacing-md;
  border-radius: 18px;
  max-width: 70%;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  font-weight: 500;
  
  &.user {
    background: linear-gradient(135deg, #2E5BBA, #1E3A8A);
    color: $pearl-white;
    margin-left: auto;
    text-align: right;
    border-bottom-right-radius: 6px;
    border: 2px solid rgba($imperial-yellow, 0.6);
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    
    .message-text {
      color: $pearl-white;
      font-weight: 500;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 12px;
      height: 12px;
      background: inherit;
      border-radius: 50%;
    }
  }
  
  &.assistant {
    background: linear-gradient(135deg, #059669, #047857);
    color: $pearl-white;
    margin-right: auto;
    border-bottom-left-radius: 6px;
    border: 2px solid rgba($bronze-gold, 0.6);
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    
    .message-text {
      color: $pearl-white;
      font-weight: 500;
    }
    
    &::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: -2px;
      width: 12px;
      height: 12px;
      background: inherit;
      border-radius: 50%;
    }
  }
}

.chat-input {
  display: flex;
  gap: $spacing-sm;
  
  input {
    flex: 1;
    padding: $spacing-md;
    border: 2px solid $cinnabar-red;
    border-radius: 25px;
    font-size: 16px;
    font-family: $font-family-serif;
    background: white;
    color: $text-primary;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:focus {
      outline: none;
      border-color: $imperial-yellow;
      box-shadow: 
        0 0 0 3px rgba($imperial-yellow, 0.2),
        0 2px 8px rgba(0,0,0,0.1);
      background: white;
      transform: scale(1.02);
    }
    
    &::placeholder {
      color: rgba($text-secondary, 0.6);
      font-style: italic;
      font-weight: normal;
    }
  }
  
  button {
    @extend .lingnan-btn;
    padding: $spacing-md $spacing-lg;
  }
}

// 响应式设计
// 传统岭南动画效果
@keyframes traditional-glow {
  0% {
    text-shadow: 0 0 5px rgba($golden-yellow, 0.5);
  }
  50% {
    text-shadow:
      0 0 10px rgba($golden-yellow, 0.8),
      0 0 20px rgba($vermillion, 0.3),
      0 0 30px rgba($deep-jade, 0.2);
  }
  100% {
    text-shadow: 0 0 5px rgba($golden-yellow, 0.5);
  }
}

@keyframes seal-pulse {
  0%, 100% {
    box-shadow:
      inset 0 2px 4px rgba(255,255,255,0.2),
      0 4px 8px rgba(0,0,0,0.3),
      0 0 0 0 rgba($golden-yellow, 0.7);
  }
  50% {
    box-shadow:
      inset 0 2px 4px rgba(255,255,255,0.3),
      0 6px 12px rgba(0,0,0,0.4),
      0 0 0 4px rgba($golden-yellow, 0.3);
  }
}

@keyframes floating-ornament {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(2deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-12px) rotate(-2deg);
  }
}

@keyframes traditional-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

// 传统纹样动画类
.traditional-animate {
  animation: traditional-glow 4s ease-in-out infinite;
}

.seal-animate {
  animation: seal-pulse 3s ease-in-out infinite;
}

.ornament-animate {
  animation: floating-ornament 6s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba($golden-yellow, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: traditional-shimmer 3s ease-in-out infinite;
}

@media (max-width: 768px) {
  .lingnan-title {
    font-size: 2rem;
  }

  .chat-container {
    padding: $spacing-md;
  }

  .message {
    max-width: 85%;
  }

  .hero-seal-container {
    flex-direction: column;
    gap: 20px;
  }

  .side-seals {
    flex-direction: row;
    gap: 20px;
  }
}