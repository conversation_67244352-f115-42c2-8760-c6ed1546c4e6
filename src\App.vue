<template>
  <div id="app" class="lingnan-pattern lingnan-brocade">
    <div class="app-container">
      <Header />
      <main class="main-content guangfu-window">
        <router-view />
      </main>
      <Footer />
    </div>
  </div>
</template>

<script setup>
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 25px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba($golden-yellow, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba($vermillion, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
}
</style>