<template>
  <header class="header traditional-border guangfu-window corner-ornaments lingnan-brocade">
    <div class="header-content">
      <div class="logo-section">
        <div class="logo-seal">
          <h1 class="site-title ink-title">岭南瑶绣</h1>
          <div class="seal-mark">印</div>
        </div>
        <p class="site-subtitle">传统工艺 · 智能传承 · 文化瑰宝</p>
        <div class="title-ornament">
          <span class="ornament-left">❋</span>
          <span class="ornament-center">◆</span>
          <span class="ornament-right">❋</span>
        </div>
      </div>
      
      <nav class="nav-menu">
        <router-link to="/" class="nav-item">首页</router-link>
        <router-link to="/embroidery" class="nav-item">瑶绣展示</router-link>
        <router-link to="/chat" class="nav-item">智能问答</router-link>
        <router-link to="/style-showcase" class="nav-item">风格展示</router-link>
        <router-link to="/about" class="nav-item">关于我们</router-link>
      </nav>
      
      <div class="cultural-elements">
        <div class="traditional-seal">
          <div class="seal-text">瑶</div>
        </div>
        <div class="decorative-pattern">❋</div>
        <div class="decorative-pattern">❀</div>
        <div class="decorative-pattern">❁</div>
        <div class="traditional-seal">
          <div class="seal-text">绣</div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
</script>

<style scoped>
.header {
  background:
    linear-gradient(135deg, rgba($ivory, 0.95), rgba($rice-paper, 0.9)),
    radial-gradient(circle at 20% 50%, rgba($deep-jade, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba($vermillion, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 20%, rgba($golden-yellow, 0.06) 0%, transparent 40%);
  padding: 25px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow:
    $shadow-md,
    0 3px 0 rgba($golden-yellow, 0.4),
    0 6px 0 rgba($bronze-gold, 0.2);
  backdrop-filter: blur(12px);
  border-bottom: 2px solid rgba($bronze-gold, 0.3);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.logo-section {
  text-align: center;
  position: relative;
}

.logo-seal {
  position: relative;
  display: inline-block;
  padding: 15px 25px;
  background:
    linear-gradient(135deg, rgba($ivory, 0.9), rgba($pearl-white, 0.8)),
    radial-gradient(circle at center, rgba($golden-yellow, 0.1) 0%, transparent 60%);
  border: 3px solid $bronze-gold;
  border-radius: 12px;
  box-shadow:
    inset 0 2px 4px rgba($golden-yellow, 0.3),
    0 4px 8px rgba(0,0,0,0.15);
  margin-bottom: 10px;
}

.site-title {
  font-family: $font-family-traditional;
  font-size: 2.2rem;
  color: $vermillion;
  margin: 0;
  font-weight: 700;
  text-shadow:
    2px 2px 4px rgba(0,0,0,0.15),
    0 0 15px rgba($golden-yellow, 0.3);
  position: relative;
  letter-spacing: 3px;
}

.seal-mark {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  color: $ivory;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  font-family: $font-family-traditional;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  border: 2px solid $golden-yellow;
}

.site-subtitle {
  font-family: $font-family-serif;
  color: $text-secondary;
  font-size: 1rem;
  margin: 8px 0;
  font-style: italic;
  letter-spacing: 1px;
}

.title-ornament {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 8px;
}

.ornament-left, .ornament-right {
  color: $jade-green;
  font-size: 1.2rem;
  animation: float 3s ease-in-out infinite;
}

.ornament-center {
  color: $golden-yellow;
  font-size: 1rem;
  animation: rotate 4s linear infinite;
}

.ornament-right {
  animation-delay: 1.5s;
}

.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-item {
  text-decoration: none;
  color: $text-primary;
  font-family: $font-family-serif;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 20px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba($jade-green, 0.1), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

.nav-item:hover {
  color: $cinnabar-red;
  background: linear-gradient(135deg, rgba($jade-green, 0.1), rgba($imperial-yellow, 0.1));
  border-color: rgba($guangfu-gold, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba($jade-green, 0.2);
}

.nav-item.router-link-active {
  color: $cinnabar-red;
  background: linear-gradient(135deg, rgba($cinnabar-red, 0.1), rgba($imperial-yellow, 0.15));
  border-color: $imperial-yellow;
  box-shadow: 0 2px 8px rgba($cinnabar-red, 0.2);
}

.nav-item.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, $cinnabar-red, $imperial-yellow, $jade-green);
  border-radius: 2px;
}

.cultural-elements {
  display: flex;
  gap: 20px;
  align-items: center;
}

.traditional-seal {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, $cinnabar-red, $vermillion);
  border: 2px solid $golden-yellow;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    inset 0 1px 2px rgba(255,255,255,0.2),
    0 3px 6px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
      inset 0 1px 2px rgba(255,255,255,0.3),
      0 5px 10px rgba(0,0,0,0.4);
  }
}

.seal-text {
  color: $ivory;
  font-family: $font-family-traditional;
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.decorative-pattern {
  font-size: 1.8rem;
  color: $vermillion;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
  text-shadow: 0 0 8px rgba($golden-yellow, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.2);
    opacity: 1;
    text-shadow: 0 0 15px rgba($golden-yellow, 0.8);
  }
}

.decorative-pattern:nth-child(2) {
  animation-delay: 1s, 0.5s;
  color: $jade-green;
}

.decorative-pattern:nth-child(3) {
  animation-delay: 2s, 1s;
  color: $porcelain-blue;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba($golden-yellow, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba($golden-yellow, 0.8), 0 0 25px rgba($vermillion, 0.3);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  .site-title {
    font-size: 1.5rem;
  }
  
  .cultural-elements {
    order: -1;
  }
}
</style>